{"permissions": {"allow": ["Bash(pnpm db:generate:*)", "Bash(pnpm lint:*)", "Bash(find:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "Bash(rm -rf \"/mnt/d/BaiduNetdiskDownload/ShipAny更新/shipany-template-main-v2.5/ai-shipany-template-main/src/lib/ai-pattern-extraction\")", "Bash(pnpm build:*)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)", "Bash(pnpm add axios)", "Bash(pnpm add xml2js)", "WebFetch(domain:docs.next-auth-oauth.ggss.club)", "Bash(pnpm add:*)", "Bash(pnpm remove:*)"], "deny": []}}
# Technology Stack

ShipAny Template One is built with a modern web development stack focused on performance, developer experience, and scalability.

## Core Technologies

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **UI**: React 19
- **Styling**: Tailwind CSS 4, shadcn/ui components
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: NextAuth.js 5
- **State Management**: React Context API and hooks
- **Animation**: Framer Motion
- **Internationalization**: next-intl

## AI Integrations

- **LLM Providers**: OpenAI, DeepSeek, OpenRouter (via AI SDK)
- **Image Generation**: F.1 Kontext API
- **Vector Database**: Optional integrations available

## Deployment Options

- **Vercel**: Primary deployment target
- **Cloudflare**: Secondary deployment option with Pages
- **Docker**: Container support for custom deployments

## Build System

- **Package Manager**: pnpm
- **Build Tool**: Next.js with Turbopack
- **Module Bundler**: Built into Next.js

## Common Commands

```bash
# Development
pnpm dev                 # Start development server with Turbopack
pnpm lint                # Run ESLint

# Database
pnpm db:generate         # Generate Drizzle migrations
pnpm db:migrate          # Run migrations
pnpm db:studio           # Open Drizzle Studio
pnpm db:push             # Push schema changes to database

# Build & Deployment
pnpm build               # Build for production
pnpm start               # Start production server
pnpm analyze             # Analyze bundle size
pnpm docker:build        # Build Docker image

# Cloudflare Deployment
pnpm cf:build            # Build for Cloudflare Pages
pnpm cf:preview          # Preview Cloudflare deployment
pnpm cf:deploy           # Deploy to Cloudflare Pages
```

## Environment Variables

Key environment variables are defined in `.env.example` and should be configured in `.env.development` for local development or `.env.production` for production deployments.

## Code Quality

- TypeScript for type safety
- ESLint for code linting
- Consistent component patterns
- Modular architecture
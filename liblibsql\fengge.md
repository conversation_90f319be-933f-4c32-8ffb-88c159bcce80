接下来我们需要实现风格绘画的功能 ui需要和我们之前实现的三栏布局一样。左侧操作栏 中间图片展示区域 右侧历史记录展示区域。然后数据先保存到本地。具体实现可以参考其他工作流的实现。
首先要有一个风格模型 也就是左侧操作栏会有一个地方选择风格模型。
![alt text](image.png)
如图所示。
这里的数据从D:\BaiduNetdiskDownload\ShipAny更新\shipany-template-main-v2.5\ai-shipany-template-main\liblibsql\ai_liblib_lora.sql这里获取。
标签tab页用基础算法类型渲染。

注意需要根据base_algorithm_type匹配下对应的模板id
f.1文生图 传入参数 这里templateUuid  steps imgCount seed restoreFaces 固定 然后lora的模板id 传给additionalNetwork这里的modelid 权重用户可以选择。还有图片比例也可以选 
{
    "templateUuid": "6f7c4652458d4802969f8d089cf5b91f", // 参数模板ID
    "generateParams": {
        // 基础参数
        "prompt": "filmfotos, Asian portrait,A young woman wearing a green baseball cap,covering one eye with her hand", // 选填
        "steps": 20, // 采样步数
        "width": 768, // 宽
        "height": 1024, // 高
        "imgCount": 1, // 图片数量    
        "seed": -1, // 随机种子值，-1表示随机    
        "restoreFaces": 0,  // 面部修复，0关闭，1开启
    
        // Lora添加，最多5个，不添加lora时请删除此结构体
        "additionalNetwork": [
            {
                "modelId": "169505112cee468b95d5e4a5db0e5669", //LoRA的模型版本uuid
                "weight": 1.0 // LoRA权重
            }
        ]
    }
}
f.1图生图 这个也一样。只有lora参数和prompt还有参考图还有图片比例是需要用户传的 其他都固定
{
    "templateUuid": "63b72710c9574457ba303d9d9b8df8bd", // 预设参数模板ID
    "generateParams": {
        // 基础参数
        "prompt": "filmfotos, 1 asian girl with beautiful face,lotus leaf,masterpiece,best quality,finely detail,highres,8k,beautiful and aesthetic,no watermark,", //正向提示词
        "steps": 20, // 采样步数
        "seed": -1, // 随机种子值，-1表示随机
        "imgCount": 1, // 1到4
        "restoreFaces": 0,  // 面部修复，0关闭，1开启
        
        // 图像相关参数
        "sourceImage": "https://liblibai-online.liblib.cloud/img/081e9f07d9bd4c2ba090efde163518f9/7c1cc38e-522c-43fe-aca9-07d5420d743e.png",
        "resizeMode": 0, // 缩放模式， 0 拉伸，1 裁剪，2 填充 
        "resizedWidth": 1024, // 图像缩放后的宽度
        "resizedHeight": 1024, // 图像缩放后的高度
        "mode": 0, // 目前只建议用图生图
        "denoisingStrength": 0.75, // 重绘幅度
        
        // Lora添加，最多5个，不添加lora时请删除此结构体
        "additionalNetwork": [
            {
                "modelId": "169505112cee468b95d5e4a5db0e5669", //LoRA的模型版本uuid
                "weight": 1.0 // LoRA权重
            }
        ]
    }
}
    

    {
    "templateUuid": "e10adc3949ba59abbe56e057f20f883e", // 参数模板ID
    "generateParams": {
        // 基础参数
        "checkPointId": "0ea388c7eb854be3ba3c6f65aac6bfd3", // 底模 modelVersionUUID
        "vaeId": "",
        "prompt": "Asian portrait,A young woman wearing a green baseball cap,covering one eye with her hand", // 选填
        "negativePrompt": "ng_deepnegative_v1_75t,(badhandv4:1.2),EasyNegative,(worst quality:2),", //选填
        "clipSkip": 2,  // 1到12，正整数值
        "sampler": 15, // 采样方法
        "steps": 20, // 采样步数
        "cfgScale": 7, // 提示词引导系数
        "width": 768, // 宽
        "height": 1024, // 高
        "imgCount": 1, // 图片数量    
        "randnSource": 0,  // 随机种子生成器 0 cpu，1 Gpu
        "seed": -1, // 随机种子值，-1表示随机    
        "restoreFaces": 0,  // 面部修复，0关闭，1开启
    
        // Lora添加，最多5个，不添加lora时请删除此结构体
        "additionalNetwork": [
            {
                "modelId": "31360f2f031b4ff6b589412a52713fcf", //LoRA的模型版本uuid
                "weight": 0.3 // LoRA权重
            },
            {
                "modelId": "365e700254dd40bbb90d5e78c152ec7f", //LoRA的模型版本uuid
                "weight": 0.6 // LoRA权重
            }
        ],
    
        // 高分辨率修复，不需要时请删除此结构体
        "hiResFixInfo": {
            "hiresSteps": 20, // 高分辨率修复的重绘步数
            "hiresDenoisingStrength": 0.75, // 高分辨率修复的重绘幅度
            "upscaler": 10, // 放大算法模型枚举
            "resizedWidth": 1024,  // 放大后的宽度
            "resizedHeight": 1536  // 放大后的高度
        },
    
        // controlNet，最多4组，不需要时请删除此结构体
        "controlNet": [
            {
                "unitOrder": 1, // 执行顺序
                "sourceImage": "https://liblibai-online.liblib.cloud/img/081e9f07d9bd4c2ba090efde163518f9/7c1cc38e-522c-43fe-aca9-07d5420d743e.png",
                "width": 1024, // 参考图宽度
                "height": 1536, // 参考图高度
                "preprocessor": 3, // 预处理器枚举值
                "annotationParameters": { // 预处理器参数， 不同预处理器不同，此处仅为示意
                    "depthLeres": { // 预处理器 对应的参数
                        "preprocessorResolution": 1024,
                        "removeNear": 0,
                        "removeBackground": 0
                    }
                },
                "model": "6349e9dae8814084bd9c1585d335c24c", // controlnet的模型
                "controlWeight": 1, // 控制权重
                "startingControlStep": 0, //开始控制步数
                "endingControlStep": 1, // 结束控制步数
                "pixelPerfect": 1, // 完美像素
                "controlMode": 0, // 控制模式 ，0 均衡，1 更注重提示词，2 更注重controlnet，
                "resizeMode": 1, // 缩放模式， 0 拉伸，1 裁剪，2 填充
                "maskImage": "" // 蒙版图
            }
        ]
    }
}

{
    "templateUuid": "9c7d531dc75f476aa833b3d452b8f7ad", // 预设参数模板ID
    "generateParams": {
        // 基础参数
        "checkPointId": "0ea388c7eb854be3ba3c6f65aac6bfd3", //底模
        "vaeId": "", //  vae模型，可为空
        "prompt": "1 girl wear sunglasses", //正向提示词
        "negativePrompt": "", //负向提示词
        "clipSkip": 2, // Clip跳过层
        "sampler": 15, //采样方法
        "steps": 20, // 采样步数
        "cfgScale": 7, // 提示词引导系数    
        "randnSource": 0, // 随机种子来源，0表示CPU，1表示GPU
        "seed": -1, // 随机种子值，-1表示随机
        "imgCount": 1, // 1到4
        "restoreFaces": 0,  // 面部修复，0关闭，1开启
        
        // 图像相关参数
        "sourceImage": "https://liblibai-online.liblib.cloud/img/081e9f07d9bd4c2ba090efde163518f9/7c1cc38e-522c-43fe-aca9-07d5420d743e.png",
        "resizeMode": 0, // 缩放模式， 0 拉伸，1 裁剪，2 填充 
        "resizedWidth": 1024, // 图像缩放后的宽度
        "resizedHeight": 1536, // 图像缩放后的高度
        "mode": 4, // 0图生图，4蒙版重绘
        "denoisingStrength": 0.75, // 重绘幅度
        
        // Lora添加，最多5个，不添加lora时请删除此结构体
        "additionalNetwork": [
            {
                "modelId": "31360f2f031b4ff6b589412a52713fcf", //LoRA的模型版本uuid
                "weight": 0.3 // LoRA权重
            },
            {
                "modelId": "365e700254dd40bbb90d5e78c152ec7f", //LoRA的模型版本uuid
                "weight": 0.6 // LoRA权重
            }
        ],
        
        // 局部重绘，上传蒙版重绘 —— 蒙版重绘相关参数，非蒙版重绘时请删除此结构体
        "inpaintParam": { 
                "maskImage": "https://liblibai-online.liblib.cloud/img/081e9f07d9bd4c2ba090efde163518f9/9f2ea8dd-a64d-459c-a952-3eba8d8be166.png", // 蒙版地址,白色蒙版，黑色底色
                "maskBlur": 4, // 蒙版模糊度
                "maskPadding": 32, //蒙版边缘预留像素，也称蒙版扩展量 
                "maskMode": 0, // 蒙版模式    
                "inpaintArea": 0, //重绘区域, 0重绘全图，1仅重绘蒙版区域
                "inpaintingFill": 1 //蒙版内容的填充模式
            },
    
        // controlNet，最多4组，不需要时请删除此结构体
            "controlNet": [
                {
                    "unitOrder": 1, // 执行顺序
                    "sourceImage": "https://liblibai-online.liblib.cloud/img/081e9f07d9bd4c2ba090efde163518f9/7c1cc38e-522c-43fe-aca9-07d5420d743e.png",
                    "width": 1024, // 参考图宽度
                    "height": 1536, // 参考图高度
                    "preprocessor": 3, // 预处理器枚举值
                    "annotationParameters": { // 预处理器参数， 不同预处理器不同，此处仅为示意
                        "depthLeres": { // 预处理器 对应的参数
                            "preprocessorResolution": 1024,
                            "removeNear": 0,
                            "removeBackground": 0
                        }
                    },
                    "model": "6349e9dae8814084bd9c1585d335c24c", // controlnet的模型
                    "controlWeight": 1, // 控制权重
                    "startingControlStep": 0, //开始控制步数
                    "endingControlStep": 1, // 结束控制步数
                    "pixelPerfect": 1, // 完美像素
                    "controlMode": 0, // 控制模式 ，0 均衡，1 更注重提示词，2 更注重controlnet，
                    "resizeMode": 1, // 缩放模式， 0 拉伸，1 裁剪，2 填充
                    "maskImage": "" // 蒙版图
                }
            ]
    }
}
这个也一样。

下面是调用接口 和参数的例子：
在登录Liblib领取API试用积分或购买API积分后，Liblib会生成开放平台访问密钥，用于后续API接口访问，密钥包括：
- AccessKey，API访问凭证，唯一识别访问用户，长度通常在20-30位左右，如：KIQMFXjHaobx7wqo9XvYKA
- SecretKey，API访问密钥，用于加密请求参数，避免请求参数被篡改，长度通常在30位以上，如：KppKsn7ezZxhi6lIDjbo7YyVYzanSu2d
2.4.1 使用密钥
申请API密钥之后，需要在每次请求API接口的查询字符串中固定传递以下参数：
参数
类型
是否必需
说明
AccessKey
String
是
开通开放平台授权的访问AccessKey
Signature
String
是
加密请求参数生成的签名，签名公式见下节“生成签名”
Timestamp
String
是
生成签名时的毫秒时间戳，整数字符串，有效期5分钟
SignatureNonce
String
是
生成签名时的随机字符串
如请求地址：https://test.xxx.com/api/genImg?AccessKey=KIQMFXjHaobx7wqo9XvYKA&Signature=test1232132&Timestamp=1725458584000&SignatureNonce=random1232
2.4.2 生成签名
签名生成公式如下：
# 1. 用"&"拼接参数
# URL地址：以上方请求地址为例，为“/api/genImg”
# 毫秒时间戳：即上节“使用密钥”中要传递的“Timestamp”
# 随机字符串：即上节“使用密钥”中要传递的“SignatureNonce”
原文 = URL地址 + "&" + 毫秒时间戳 + "&" + 随机字符串
# 2. 用SecretKey加密原文，使用hmacsha1算法
密文 = hmacSha1(原文, SecretKey)
# 3. 生成url安全的base64签名
# 注：base64编码时不要补全位数
签名 = encodeBase64URLSafeString(密文)

- 接口：POST /api/generate/webui/text2img
- 接口：POST /api/generate/webui/img2img
当用户有上传参考图的时候 则调用图生图接口 否则 调用文字生图接口

返回格式 {
    "code": 0,
    "msg": "",
    "data": {
        "generateUuid": "8dcbfa2997444899b71357ccb7db378b"
    }
}

查询接口- 接口：POST /api/generate/webui/status
- eaders：
header
value
备注
Content-Type
application/json

- 请求body：
参数
类型
是否必需
备注
generateUuid
string
是
生图任务uuid，发起生图任务时返回该字段
- 返回值：
参数
类型
备注
generateUuid
string
生图任务uuid，使用该uuid查询生图进度
generateStatus
int
生图状态见下方3.3.1节
percentCompleted
float
生图进度，0到1之间的浮点数，(暂未实现生图进度)
generateMsg
string
生图信息，提供附加信息，如生图失败信息
pointsCost
int
本次生图任务消耗积分数
accountBalance
int
账户剩余积分数
images
[]object
图片列表，只提供审核通过的图片
images.0.imageUrl
string
图片地址，可直接访问，地址有时效性：7天
images.0.seed
int
随机种子值
images.0.auditStatus
int
{
    "code": 0,
    "msg": "",
    "data": {
        "generateUuid": "8dcbfa2997444899b71357ccb7db378b",
        "generateStatus": 5,
        "percentCompleted": 0,
        "generateMsg": "",
        "pointsCost": 10,// 本次任务消耗积分数
        "accountBalance": 1356402,// 账户剩余积分数
        "images": [
            {
                "imageUrl": "https://liblibai-online.liblib.cloud/sd-images/08efe30c1cacc4bb08df8585368db1f9c082b6904dd8150e6e0de5bc526419ee.png",
                "seed": 12345,
                "auditStatus": 3
            }
        ]
    }
}
# Project Structure

ShipAny Template One follows a modular, feature-based organization pattern to maintain scalability and separation of concerns.

## Root Directory Structure

- `/.kiro`: Kiro AI assistant configuration and specs
- `/.next`: Next.js build output (generated)
- `/public`: Static assets
- `/src`: Application source code
- `/node_modules`: Dependencies (generated)

## Source Code Organization

The `/src` directory contains the following key areas:

### App Structure

- `/src/app`: Next.js App Router pages and API routes
  - `/[locale]`: Internationalized routes
    - `/(auth)`: Authentication pages
    - `/(dashboard)`: Dashboard pages and features
    - `/(landing)`: Public landing pages
  - `/api`: Backend API endpoints

### Core Components

- `/src/components`: React components
  - `/ui`: Base UI components (shadcn/ui)
  - `/layout`: Layout components
  - `/[feature-name]`: Feature-specific components

### Business Logic

- `/src/lib`: Utility functions and shared logic
- `/src/hooks`: Custom React hooks
- `/src/contexts`: React context providers
- `/src/services`: Service layer for external APIs

### Data Layer

- `/src/db`: Database schema and queries
- `/src/models`: Data models and types
- `/src/auth`: Authentication configuration

### Other Directories

- `/src/aisdk`: AI provider integrations
- `/src/i18n`: Internationalization configuration
- `/src/providers`: React providers
- `/src/types`: TypeScript type definitions

## Feature Organization

Each major feature typically includes:

1. Page components in `/src/app/[locale]/(dashboard)/[feature-name]`
2. API routes in `/src/app/api/[feature-name]`
3. UI components in `/src/components/[feature-name]`
4. Utility functions in `/src/lib/[feature-name]`
5. Type definitions in `/src/types/[feature-name].d.ts`

## Naming Conventions

- **Files**: Use kebab-case for file names
- **Components**: Use PascalCase for component names
- **Functions**: Use camelCase for function names
- **Types/Interfaces**: Use PascalCase with descriptive names
- **API Routes**: Organized by feature in `/api/[feature-name]`

## Code Organization Principles

1. **Feature Cohesion**: Related code is grouped by feature
2. **Separation of Concerns**: UI, logic, and data access are separated
3. **Reusability**: Common components and utilities are extracted
4. **Discoverability**: Consistent naming and organization patterns
# 微信Webhook部署解决方案

## 问题分析
ngrok免费版的限制导致微信公众号后台配置webhook失败：
- 2小时会话限制，URL频繁变化
- 随机子域名，无法固定URL
- 连接不稳定，影响微信服务器验证

## 解决方案

### 方案1：升级ngrok付费版 (推荐)
```bash
# 注册ngrok账号并升级到Personal Plan ($8/月)
# 获得固定域名和稳定连接
ngrok http 3000 --domain=your-custom-domain.ngrok-free.app
```

优点：
- 固定域名，无需频繁更改微信配置
- 无2小时限制
- 更稳定的连接
- 官方支持

### 方案2：使用免费替代工具

#### LocalTunnel (免费)
```bash
npm install -g localtunnel
lt --port 3000 --subdomain your-project-name
```
URL: `https://your-project-name.loca.lt`

#### Serveo (免费)
```bash
ssh -R 80:localhost:3000 serveo.net
```
可指定子域名: `ssh -R your-name:80:localhost:3000 serveo.net`

#### Cloudflare Tunnel (免费)
```bash
# 安装cloudflared
npm install -g cloudflared

# 创建隧道
cloudflared tunnel --url http://localhost:3000
```

### 方案3：云服务器部署 (长期方案)

#### 购买便宜VPS
- 阿里云轻量应用服务器 (24元/月)
- 腾讯云轻量应用服务器 (25元/月)  
- 华为云弹性云服务器

#### 使用Vercel部署 (免费)
```bash
# 部署到Vercel获得固定域名
npm install -g vercel
vercel --prod
```

## 立即解决方案

### 1. 使用LocalTunnel测试
```bash
# 安装
npm install -g localtunnel

# 启动应用
pnpm dev

# 新终端窗口启动隧道
lt --port 3000 --subdomain mihui-ai

# 获得固定URL: https://mihui-ai.loca.lt
```

### 2. 更新微信配置
将webhook URL更改为: `https://mihui-ai.loca.lt/api/wechat/webhook`

### 3. 验证配置
```bash
# 测试URL可访问性
curl -I https://mihui-ai.loca.lt/api/wechat/test

# 测试微信签名验证
node src/lib/wechat-signature.ts
```

## 推荐步骤

1. **立即方案**: 使用LocalTunnel快速获得稳定URL
2. **短期方案**: 升级ngrok付费版 ($8/月)
3. **长期方案**: 部署到云服务器或Vercel

## 注意事项

- LocalTunnel首次访问需要点击"Click to Continue"
- 所有隧道工具都需要保持本地开发服务器运行
- 生产环境建议使用云服务器部署
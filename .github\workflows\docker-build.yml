name: Build and Push Docker Image

on:
  push:
    branches:
      - main   # 根据你的默认分支改
  workflow_dispatch: # 支持手动触发

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      # 检出源码
      - name: Checkout code
        uses: actions/checkout@v4

      # 登录到 Docker Hub
      - name: Log in to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      # 设置 buildx（支持缓存和多架构）
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      # 构建并推送镜像
      - name: Build and Push Docker Image
        uses: docker/build-push-action@v4
        with:
          context: .                      # 当前目录
          file: ./Dockerfile               # 使用你的 Dockerfile
          push: true
          tags: mydockeruser/saas-client-b:latest

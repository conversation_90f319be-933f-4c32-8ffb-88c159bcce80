# Design Document

## Overview

The AI Image Upscale feature provides users with the ability to enhance image resolution using advanced AI upscaling models through the LiblibAI ComfyUI API. The feature follows the established three-column layout pattern from the existing AI image generation feature, ensuring consistency in user experience and code architecture.

The system integrates with the LiblibAI ComfyUI API using templateUuid "4df2efa0f18d46dc9758803e478eb51c" and supports 25 different upscaling models with configurable parameters including scale factor (0.05-4.0) and sampling steps (1-10000).

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[User Interface] --> B[React Components]
    B --> C[Custom Hooks]
    C --> D[API Routes]
    D --> E[LiblibAI ComfyUI API]
    D --> F[Database Layer]
    F --> G[PostgreSQL]
    
    H[File Upload] --> I[Temporary Storage]
    I --> E
    
    J[Credit System] --> D
    K[Authentication] --> D
```

### Component Architecture

The feature follows the established component structure from ai-image-generation:

- **Page Component**: Main orchestrator following the three-column layout
- **Control Panel**: Left sidebar with upload and parameter controls
- **Display Area**: Central area for progress and results
- **History Panel**: Right sidebar for history management

### Data Flow

1. **Upload Flow**: User uploads image → Temporary storage → LiblibAI API
2. **Processing Flow**: API request → Status polling → Result display
3. **History Flow**: Successful results → Database storage → History panel

## Components and Interfaces

### Core Components

#### 1. Main Page Component (`AIImageUpscalePage`)
```typescript
interface AIImageUpscalePageProps {
  // No props - uses authentication context for user data
}
```

**Responsibilities:**
- Orchestrate the three-column layout
- Manage global state and user interactions
- Handle toast notifications and error boundaries

#### 2. Control Panel Components

**UpscaleControlPanel**
```typescript
interface UpscaleControlPanelProps {
  formState: UpscaleFormState;
  onFormChange: (field: keyof UpscaleFormState, value: any) => void;
  onSubmit: () => void;
  isProcessing: boolean;
  errors: FormErrors;
}
```

**ImageUploadArea**
```typescript
interface ImageUploadAreaProps {
  image: string | null;
  onChange: (imageUrl: string) => void;
  disabled: boolean;
  error?: string;
}
```

**ModelSelector**
```typescript
interface ModelSelectorProps {
  value: UpscaleModel;
  onChange: (model: UpscaleModel) => void;
  disabled: boolean;
}
```

**ParameterControls**
```typescript
interface ParameterControlsProps {
  scaleBy: number;
  steps: number;
  onScaleByChange: (value: number) => void;
  onStepsChange: (value: number) => void;
  disabled: boolean;
  errors: {
    scaleBy?: string;
    steps?: string;
  };
}
```

#### 3. Display Area Components

**UpscaleDisplay**
```typescript
interface UpscaleDisplayProps {
  originalImage: string | null;
  upscaledImage: string | null;
  isProcessing: boolean;
  progress: number;
  onDownload: () => void;
  onSave: () => void;
}
```

**ProcessingProgress**
```typescript
interface ProcessingProgressProps {
  progress: number;
  canCancel: boolean;
  onCancel: () => void;
  estimatedTimeRemaining: number;
}
```

**ImageComparison**
```typescript
interface ImageComparisonProps {
  originalImage: string;
  upscaledImage: string;
  originalSize: { width: number; height: number };
  upscaledSize: { width: number; height: number };
}
```

#### 4. History Panel Components

**UpscaleHistoryPanel**
```typescript
interface UpscaleHistoryPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onOpen: () => void;
  userId: string;
  onSelectRecord: (record: UpscaleHistoryRecord) => void;
  onReuseParameters: (parameters: UpscaleParameters) => void;
}
```

### Custom Hooks

#### useImageUpscale
```typescript
interface UseImageUpscaleReturn {
  formState: UpscaleFormState;
  uiState: UpscaleUIState;
  formErrors: FormErrors;
  upscaleResult: UpscaleResult | null;
  isFormValid: boolean;
  updateFormField: (field: keyof UpscaleFormState, value: any) => void;
  updateParameters: (parameters: Partial<UpscaleParameters>) => void;
  startUpscale: () => Promise<void>;
  cancelUpscale: () => void;
  loadFromHistory: (record: UpscaleHistoryRecord) => void;
  clearErrors: () => void;
}
```

#### useUpscaleHistory
```typescript
interface UseUpscaleHistoryReturn {
  history: UpscaleHistoryRecord[];
  isLoading: boolean;
  error: string | null;
  loadHistory: () => Promise<void>;
  deleteRecord: (id: string) => Promise<void>;
  filterHistory: (filters: HistoryFilters) => void;
}
```

## Data Models

### TypeScript Interfaces

#### Core Types
```typescript
// Upscale models enum
export type UpscaleModel = 
  | 'ESRGAN_4x'
  | 'LDSR'
  | 'R-ESRGAN_4x+'
  | 'R-ESRGAN_4x+ Anime6B'
  | 'ScuNET GAN'
  | 'ScuNET PSNR'
  | 'SwinIR_4x'
  | '4x-UltraSharp'
  | '4x_NMKD-Siax_200k'
  | '4x_NMKD-Superscale-SP_178000_G'
  | '4x-AnimeSharp'
  | '4x_foolhardy_Remacri'
  | 'BSRGAN'
  | 'DAT 2'
  | 'DAT 3'
  | 'DAT 4'
  | '4x-DeCompress'
  | '4x-DeCompress-Strong'
  | '8x-NMKD-Superscale'
  | '4xNomos2_hq_dat2.pth'
  | '8x_NMKD-Faces_160000_G'
  | '4xNomos8kSCHAT-L'
  | '4xRealWebPhoto_v4'
  | '4xFaceUpSharpDAT'
  | '2xNomosUni_span_multijpg_ldl';

// Generation status from LiblibAI API
export enum GenerateStatus {
  WAITING = 1,
  PROCESSING = 2,
  GENERATED = 3,
  AUDITING = 4,
  SUCCESS = 5,
  FAILED = 6
}

// Audit status from LiblibAI API
export enum AuditStatus {
  PENDING = 1,
  AUDITING = 2,
  APPROVED = 3,
  REJECTED = 4,
  FAILED = 5
}

// Form state
export interface UpscaleFormState {
  originalImage: string | null;
  selectedModel: UpscaleModel;
  scaleBy: number;
  steps: number;
}

// Parameters for upscaling
export interface UpscaleParameters {
  model: UpscaleModel;
  scaleBy: number;
  steps: number;
}

// API request structure
export interface UpscaleRequest {
  templateUuid: string;
  generateParams: {
    30: {
      class_type: 'UltimateSDUpscale';
      inputs: {
        upscale_by: number;
        steps: number;
      };
    };
    40: {
      class_type: 'LoadImage';
      inputs: {
        image: string;
      };
    };
    41: {
      class_type: 'UpscaleModelLoader';
      inputs: {
        model_name: UpscaleModel;
      };
    };
    workflowUuid: string;
  };
}

// API response structure
export interface UpscaleResponse {
  code: number;
  data: {
    accountBalance: number;
    generateStatus: GenerateStatus;
    generateUuid: string;
    images: Array<{
      auditStatus: AuditStatus;
      imageUrl: string;
      nodeId: string;
      outputName: string;
    }>;
    percentCompleted: number;
    pointsCost: number;
    videos: any[];
  };
  msg: string;
}

// History record
export interface UpscaleHistoryRecord {
  id: string;
  uuid: string;
  userId: string;
  originalImageUrl: string;
  resultImageUrl: string | null;
  modelName: UpscaleModel;
  scaleBy: number;
  steps: number;
  generateUuid: string | null;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  generateStatus: GenerateStatus;
  percentCompleted: number;
  pointsCost: number;
  errorMessage: string | null;
  createdAt: Date;
  updatedAt: Date;
}
```

### Database Schema

#### ImageUpscaleHistory Table
```sql
CREATE TABLE image_upscale_history (
  id SERIAL PRIMARY KEY,
  uuid VARCHAR(255) NOT NULL UNIQUE,
  user_uuid VARCHAR(255) NOT NULL,
  original_image_url VARCHAR(500) NOT NULL,
  result_image_url VARCHAR(500),
  model_name VARCHAR(100) NOT NULL,
  scale_by DECIMAL(4,2) NOT NULL,
  steps INTEGER NOT NULL,
  generate_uuid VARCHAR(255),
  status VARCHAR(50) NOT NULL DEFAULT 'pending',
  generate_status INTEGER NOT NULL DEFAULT 1,
  percent_completed DECIMAL(3,2) NOT NULL DEFAULT 0.00,
  points_cost INTEGER NOT NULL DEFAULT 0,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX image_upscale_user_uuid_idx ON image_upscale_history(user_uuid);
CREATE INDEX image_upscale_created_at_idx ON image_upscale_history(created_at);
CREATE INDEX image_upscale_generate_uuid_idx ON image_upscale_history(generate_uuid);
```

## Error Handling

### Error Types
```typescript
export enum UpscaleErrorType {
  NETWORK_ERROR = 'network_error',
  AUTH_ERROR = 'auth_error',
  INVALID_PARAMS = 'invalid_params',
  UPSCALE_FAILED = 'upscale_failed',
  INSUFFICIENT_CREDITS = 'insufficient_credits',
  FILE_UPLOAD_ERROR = 'file_upload_error',
  API_RATE_LIMIT = 'api_rate_limit'
}
```

### Error Handling Strategy

1. **Network Errors**: Retry mechanism with exponential backoff
2. **Validation Errors**: Real-time form validation with user feedback
3. **API Errors**: Graceful degradation with informative messages
4. **Credit Errors**: Pre-validation and clear messaging
5. **Upload Errors**: File validation and retry options

### Error Recovery

- **Automatic Retry**: For transient network issues
- **Manual Retry**: For user-initiated actions
- **Graceful Fallback**: Maintain partial functionality when possible
- **Clear Messaging**: Provide actionable error messages

## Testing Strategy

### Unit Testing
- Component rendering and interaction
- Hook functionality and state management
- Utility function validation
- API request/response handling

### Integration Testing
- End-to-end upscaling workflow
- Database operations
- API integration with LiblibAI
- Credit system integration

### Performance Testing
- Image upload and processing performance
- Memory usage optimization
- API response time monitoring
- Database query optimization

### Test Coverage Goals
- Components: 90%+ coverage
- Hooks: 95%+ coverage
- Utilities: 100% coverage
- API routes: 90%+ coverage

## Security Considerations

### Authentication & Authorization
- User session validation for all API endpoints
- Rate limiting to prevent abuse
- Input sanitization and validation

### Data Protection
- Secure image upload handling
- Temporary file cleanup
- User data isolation

### API Security
- Request signing for LiblibAI API
- Error message sanitization
- Secure credential management

## Performance Optimization

### Image Handling
- Progressive image loading
- Thumbnail generation for history
- Lazy loading for large images
- Memory-efficient image processing

### API Optimization
- Request debouncing
- Response caching where appropriate
- Optimistic UI updates
- Background status polling

### Database Optimization
- Indexed queries for history retrieval
- Pagination for large datasets
- Soft deletion for data retention
- Query optimization for filters

## Responsive Design

### Breakpoint Strategy
- Mobile: < 768px (single column, collapsible panels)
- Tablet: 768px - 1024px (two column, side panels)
- Desktop: > 1024px (three column layout)

### Mobile Optimizations
- Touch-friendly controls
- Optimized image viewing
- Collapsible side panels
- Gesture support for image comparison

### Accessibility
- ARIA labels for screen readers
- Keyboard navigation support
- High contrast mode compatibility
- Focus management for modals
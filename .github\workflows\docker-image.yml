name: Build and Push Docker Image

on:
  push:
    branches:
      - main   # 监听 main 分支推送，也可以改成 master
  workflow_dispatch: # 支持手动触发

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'pnpm'

      - name: Install pnpm
        run: npm install -g pnpm

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build project
        run: |
          export NEXT_TELEMETRY_DISABLED=1
          export NODE_OPTIONS="--max-old-space-size=2048"
          pnpm build

      - name: Log in to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and Push Docker Image
        uses: docker/build-push-action@v4
        with:
          push: true
          tags: myuser/saas-client-b:latest

-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-07-26 21:41:37
-- 服务器版本： 5.7.40-log
-- PHP 版本： 8.0.26

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `webai`
--

-- --------------------------------------------------------

--
-- 表的结构 `ai_liblib_checkpoint`
--

CREATE TABLE `ai_liblib_checkpoint` (
  `id` int(11) NOT NULL COMMENT '主键ID',
  `application_direction` varchar(255) DEFAULT NULL COMMENT '应用方向',
  `algorithm_type` varchar(255) DEFAULT NULL COMMENT '算法类型',
  `model_name` varchar(255) DEFAULT NULL COMMENT '模型名称',
  `model_version` varchar(255) DEFAULT NULL COMMENT '模型版本',
  `model_url` varchar(255) DEFAULT NULL COMMENT '模型URL',
  `model_uuid` varchar(255) DEFAULT NULL COMMENT '模型UUID',
  `effect_ref` varchar(255) DEFAULT NULL COMMENT '效果参考',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `text_to_image_uuid` varchar(255) DEFAULT NULL COMMENT '文生图UUID',
  `image_to_image_uuid` varchar(255) DEFAULT NULL COMMENT '图生图UUID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI模型检查点表';

--
-- 转存表中的数据 `ai_liblib_checkpoint`
--

INSERT INTO `ai_liblib_checkpoint` (`id`, `application_direction`, `algorithm_type`, `model_name`, `model_version`, `model_url`, `model_uuid`, `effect_ref`, `create_time`, `text_to_image_uuid`, `image_to_image_uuid`) VALUES
(1, '通用', '基础算法 F.1', 'F.1基础算法模型-哩布在线可运行', 'F.1-dev-fp8', 'https://www.liblib.art/modelinfo/488cd9d58cd4421b9e8000373d7da123', '412b427ddb674b4dbab9e5abd5ae6057', 'https://liblibai-online.liblib.cloud/img/c86b535ec32892da5e6cfa5417ba1da5/e4958f18cbd9d40d32a13718a9beb4312303850ce0d6872d27a3d4eb1c2bef6e.jpg', '2025-03-20 19:37:45', '6f7c4652458d4802969f8d089cf5b91f', '63b72710c9574457ba303d9d9b8df8bd'),
(2, '通用', '基础算法 XL', 'Dream Tech XL | 筑梦工业XL', 'v6.0 - 寄语星河', 'https://www.liblib.art/modelinfo/5611e2f826be47f5b8c7eae45ed5434a', '0ea388c7eb854be3ba3c6f65aac6bfd3', 'https://liblibai-online.liblib.cloud/img/4c208e8657b548b5ba941c34d2166d71/7b4b3be07c1acf6387b40671dd6b83b855a4c2f3bdc4f94df00ce714dad722f3.png?x-oss-process=image/resize,w_764,m_lfit/format,webp', '2025-03-20 19:37:45', 'e10adc3949ba59abbe56e057f20f883e', '9c7d531dc75f476aa833b3d452b8f7ad'),
(3, '通用', '基础算法 XL', 'Dream Tech XL | 筑梦工业XL', 'v5.0 - 与光同尘', 'https://www.liblib.art/modelinfo/5611e2f826be47f5b8c7eae45ed5434a', 'a57911b5dfe64c6aa78821be99367276', 'https://liblibai-online.liblib.cloud/img/4c208e8657b548b5ba941c34d2166d71/7b4b3be07c1acf6387b40671dd6b83b855a4c2f3bdc4f94df00ce714dad722f3.png?x-oss-process=image/resize,w_764,m_lfit/format,webp', '2025-03-20 19:37:45', 'e10adc3949ba59abbe56e057f20f883e', '9c7d531dc75f476aa833b3d452b8f7ad'),
(4, '人像摄影', '基础算法 XL', 'AWPortrait XL', '1.1', 'https://www.liblib.art/modelinfo/f8b990b20cb943e3aa0e96f34099d794', '21df5d84cca74f7a885ba672b5a80d19', 'https://liblibai-online.liblib.cloud/img/43d9ee875a6288176ac10d689a2770c7/1509e577beca9eb7290d01dbbef9338f3e1c8f7d7aaea8655263ace1657edd15.jpg?x-oss-process=image/resize,w_764,m_lfit/format,webp', '2025-03-20 19:37:45', 'e10adc3949ba59abbe56e057f20f883e', '9c7d531dc75f476aa833b3d452b8f7ad'),
(5, '现代创意插画', '基础算法 1.5', 'ComicTrainee丨动漫插画模型', 'v2.0', 'https://www.liblib.art/modelinfo/d6053875cca7478a8ab39522b4e7cc1a', 'c291e0d339f44a98a973f138e6b0b9dc', 'https://liblibai-online.liblib.cloud/img/46351bd10e7906150572e3c7b322fa70/4f3609f1a8e2e62bae07c5f616f4e846fbe0d4a4d8384369b11e52c6a736f118.png?x-oss-process=image/resize,w_764,m_lfit/format,webp', '2025-03-20 19:37:45', 'e10adc3949ba59abbe56e057f20f883e', '9c7d531dc75f476aa833b3d452b8f7ad'),
(6, '现代创意插画', '基础算法 XL', 'niji-动漫二次元-sdxl', '2', 'https://www.liblib.art/modelinfo/3ecd30364b564a7cadbf4f7f7e7110cf', 'bd065cff3a854af2b28659ed0f6d289d', 'https://liblibai-online.liblib.cloud/img/d818a9c36e0b3a02018e2bdc8d622f28/d8fc7670b13809091541853a700f1bc9e783ed1c875171eeb36c79df836e4bed.png?x-oss-process=image/resize,w_764,m_lfit/format,webp', '2025-03-20 19:37:45', 'e10adc3949ba59abbe56e057f20f883e', '9c7d531dc75f476aa833b3d452b8f7ad'),
(7, '现代创意插画', '基础算法 XL', 'Neta Art XL 二次元角色 （更新V2）', 'V2.0', 'https://www.liblib.art/modelinfo/55b06e35dd724862b3524ff00b069fe8', 'bfb95ad44a2c4d88963d3147de547600', 'https://liblibai-online.liblib.cloud/img/b6b0a317a417452dbea216eb5290a7ef/d75870a16c1747a40ee7e758fa5c53f0d51f0648cf7861a98646f85fce72fd64.png?x-oss-process=image/resize,w_764,m_lfit/format,webp', '2025-03-20 19:37:45', 'e10adc3949ba59abbe56e057f20f883e', '9c7d531dc75f476aa833b3d452b8f7ad'),
(8, '视觉海报', '基础算法 XL', '真境写真XL Elite KV | 电商产品摄影海报视觉设计', 'VisionX 万物绘', 'https://www.liblib.art/modelinfo/75656a71d6c3448cb621d03f67198f6b', 'dfe59b044783487e8fb0800fc4e8ccc3', 'https://liblibai-online.liblib.cloud/img/289dfb82dbb444dab196cde0d1d39b7a/021c2659c8dcbfc879042ae450e5b41037e0ec30fc6987624d5c37773f52677e.jpg?x-oss-process=image/resize,w_764,m_lfit/format,webp', '2025-03-20 19:37:45', 'e10adc3949ba59abbe56e057f20f883e', '9c7d531dc75f476aa833b3d452b8f7ad'),
(9, '建筑设计', '基础算法 1.5', '城市设计大模型 | UrbanDesign', 'v7', 'https://www.liblib.art/modelinfo/5e1b4ea7f9554e46b2509f59269b1ea8', 'f40405b7404a455db689a6646a75c103', 'https://liblibai-online.liblib.cloud/web/image/cb71159df37bd28fc94d70e0a365498ec45f0c60cf69c1b8599a12044de03a8c.png?x-oss-process=image/resize,w_764,m_lfit/format,webp', '2025-03-20 19:37:45', 'e10adc3949ba59abbe56e057f20f883e', '9c7d531dc75f476aa833b3d452b8f7ad'),
(10, '建筑设计', '基础算法 XL', '比鲁斯大型建筑大模型', 'XL0.35_PRO', 'https://www.liblib.art/modelinfo/a7177a52c3e74e04a65aff5bab87d01a', 'd3bfdeba43bc4b5ca44e35d9fcd2f487', 'https://liblibai-online.liblib.cloud/img/d65d6e10789f47dd9eb99f60b560fd02/e2b787bf335ff4be5b71a20e806025b843cd3270f96c8fd8e20a88f2c9ae8388.png?x-oss-process=image/resize,w_764,m_lfit/format,webp', '2025-03-20 19:37:45', 'e10adc3949ba59abbe56e057f20f883e', '9c7d531dc75f476aa833b3d452b8f7ad');

--
-- 转储表的索引
--

--
-- 表的索引 `ai_liblib_checkpoint`
--
ALTER TABLE `ai_liblib_checkpoint`
  ADD PRIMARY KEY (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

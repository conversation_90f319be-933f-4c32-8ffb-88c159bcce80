接口请求：
POSThttps://openapi.liblibai.cloud/api/generate/comfyui/app
参数示例
JSON
复制
{
    "templateUuid": "4df2efa0f18d46dc9758803e478eb51c",
    "generateParams": {
        "543": {
            "class_type": "LoadImage",
            "inputs": {
                "image": "https://liblibai-tmp-image.liblib.cloud/img/444030ead938486c89bcd731e28f1ca6/8a39533dbb65dd97ffe6fea0c4fc4cc2eca61a0e332069169d52f81cb32cfd07.png"
            }
        },
        "580": {
            "class_type": "Primitive string multiline [Crystools]",
            "inputs": {
                "string": "地毯"
            }
        },
        "workflowUuid": "309906cb93af4c66960625b232cae4a5"
    }
}
参数说明
节点ID	节点类型	节点名称	参数项	参数名称	参数说明
543	LoadImage	加载图像	
image
图像
{
  "name": "image",
  "displayName": "图像",
  "type": "IMAGE",
  "defaultValue": "img/444030ead938486c89bcd731e28f1ca6/8a39533dbb65dd97ffe6fea0c4fc4cc2eca61a0e332069169d52f81cb32cfd07.png",
  "image_upload": true,
  "parentId": 543,
  "id": "image",
  "isMaskImage": false
}
580	Primitive string multiline [Crystools]	需要提取的主体	
string
需要提前的主体
{
  "name": "string",
  "displayName": "需要提前的主体",
  "type": "STRING",
  "multiline": true,
  "defaultValue": "地毯",
  "id": "string",
  "parentId": 580
}
查询生图结果
POSThttps://openapi.liblibai.cloud/api/generate/comfy/status
查询结果示意
JSON
复制
{
        "code": 0,
        "data": {
                "accountBalance": *********,
                "generateStatus": 5,
                "generateUuid": "4abdd10c0f70477ebfd19fb23968544c",
                "images": [
                        {
                                "auditStatus": 3,
                                "imageUrl": "https://liblibai-tmp-image.liblib.cloud/img/360643a3d8414af8b99664b208bc9302/2c8f72ab1b06ad9c77ab73aaeafcbb455a329f6acb7e4e3471300256d9ad7ac6.png",
                                "nodeId": "552",
                                "outputName": "SaveImage"
                        },
                        {
                                "auditStatus": 3,
                                "imageUrl": "https://liblibai-tmp-image.liblib.cloud/img/360643a3d8414af8b99664b208bc9302/18d7a5b581f5253e69f67d56aefef7c7d50b126dab50d578cc06b07fef5b110a.png",
                                "nodeId": "550",
                                "outputName": "PreviewImage"
                        }
                ],
                "percentCompleted": 1,
                "pointsCost": 12,
                "videos": []
        },
        "msg": ""
}}
返回值说明
参数	类型	备注
generateUuid	string	生图任务uuid，使用该uuid查询生图进度
generateStatus	int	
生图任务的执行状态：
1：等待执行
2：执行中
3：已生图
4：审核中
5：任务成功
6：任务失败
percentCompleted	float	生图进度，0到1之间的浮点数
generateMsg	string	生图信息，提供附加信息，如生图失败信息
images	[]object	图片列表，只提供审核通过的图片
images.0.imageUrl	string	图片地址，可直接访问，地址有时效性：7天
images.0.auditStatus	int	
审核状态：
1：待审核
2：审核中
3：审核通过
4：审核拦截
5：审核失败
videos	[]object	图片列表，只提供审核通过的图片
videos.0.videoUrl	string	视频列表，只提供审核通过的视频
videos.0.coverPath	string	视频地址，可直接访问，地址有时效性：7天
videos.0.nodeId	string	输出视频的节点ID（可忽略）
videos.0.outputName	string	输出视频的节点名称
videos.0.auditStatus	int	
审核状态：
1：待审核
2：审核中
3：审核通过
4：审核拦截
5：审核失败
2.4 生成API密钥
在登录Liblib领取API试用积分或购买API积分后，Liblib会生成开放平台访问密钥，用于后续API接口访问，密钥包括：
- AccessKey，API访问凭证，唯一识别访问用户，长度通常在20-30位左右，如：KIQMFXjHaobx7wqo9XvYKA
- SecretKey，API访问密钥，用于加密请求参数，避免请求参数被篡改，长度通常在30位以上，如：KppKsn7ezZxhi6lIDjbo7YyVYzanSu2d
2.4.1 使用密钥
申请API密钥之后，需要在每次请求API接口的查询字符串中固定传递以下参数：
参数
类型
是否必需
说明
AccessKey
String
是
开通开放平台授权的访问AccessKey
Signature
String
是
加密请求参数生成的签名，签名公式见下节“生成签名”
Timestamp
String
是
生成签名时的毫秒时间戳，整数字符串，有效期5分钟
SignatureNonce
String
是
生成签名时的随机字符串
如请求地址：https://test.xxx.com/api/genImg?AccessKey=KIQMFXjHaobx7wqo9XvYKA&Signature=test1232132&Timestamp=1725458584000&SignatureNonce=random1232
2.4.2 生成签名
签名生成公式如下：
# 1. 用"&"拼接参数
# URL地址：以上方请求地址为例，为“/api/genImg”
# 毫秒时间戳：即上节“使用密钥”中要传递的“Timestamp”
# 随机字符串：即上节“使用密钥”中要传递的“SignatureNonce”
原文 = URL地址 + "&" + 毫秒时间戳 + "&" + 随机字符串
# 2. 用SecretKey加密原文，使用hmacsha1算法
密文 = hmacSha1(原文, SecretKey)
# 3. 生成url安全的base64签名
# 注：base64编码时不要补全位数
签名 = encodeBase64URLSafeString(密文)
# Design Document

## Overview

The AI Pattern Extraction feature will be implemented as a new dashboard page that integrates with the LiblibAI ComfyUI API to provide advanced pattern extraction capabilities. The feature follows the existing project architecture using Next.js 15, React 19, TypeScript, and the established UI component patterns with Radix UI and Tailwind CSS.

The design leverages the existing three-column layout pattern seen in the ai-image-generation page, with enhancements for the LiblibAI ComfyUI API integration, database persistence, and improved user experience for pattern extraction workflows.

## Architecture

### Frontend Architecture
- **Framework**: Next.js 15 with App Router
- **UI Framework**: React 19 with TypeScript
- **Styling**: Tailwind CSS with custom component library
- **State Management**: React hooks (useState, useEffect, useRef)
- **Animation**: Framer Motion for smooth transitions
- **UI Components**: Radix UI primitives with custom styling

### Backend Architecture
- **API Route**: `/api/ai-pattern-extraction` following existing patterns
- **Authentication**: NextAuth.js session validation
- **Credits System**: Integration with existing credit management
- **External API**: LiblibAI ComfyUI API integration
- **Database**: PostgreSQL with Drizzle ORM for history persistence
- **Storage**: File upload handling and temporary storage

### Navigation Integration
- New menu item added to dashboard sidebar navigation
- Route: `/ai-pattern-extraction`
- Icon: `RiImageEditLine` (following existing icon patterns)

## Components and Interfaces

### Frontend Components

#### 1. 主页面组件 (AIPatternExtractionPage)
```typescript
interface AIPatternExtractionPageProps {
  // 主页面不需要额外props
}
```

#### 2. 上传区域组件 (PatternUploadArea)
```typescript
interface PatternUploadAreaProps {
  onImageUpload: (file: File) => void;
  isProcessing: boolean;
  uploadedImage?: string;
  error?: string;
}
```

#### 3. 图片展示组件 (PatternDisplayArea)
```typescript
interface PatternDisplayAreaProps {
  originalImage?: string;
  extractedPatterns: ExtractedPattern[];
  isLoading: boolean;
  onPatternSelect: (pattern: ExtractedPattern) => void;
  onDownload: (pattern: ExtractedPattern) => void;
  onDownloadAll: () => void;
}
```

#### 4. 历史记录组件 (PatternHistoryPanel)
```typescript
interface PatternHistoryPanelProps {
  userId: string;
  isOpen: boolean;
  onClose: () => void;
  onOpen: () => void;
  onSelectRecord: (record: PatternExtractionRecord) => void;
  onDeleteRecord: (recordId: string) => void;
}
```

### API Interfaces

#### 1. 图案提取请求
```typescript
interface PatternExtractionRequest {
  imageFile: File;
  extractionSubject: string; // 需要提取的主体描述，如"地毯"、"花纹"等
}
```

#### 2. 图案提取响应
```typescript
interface PatternExtractionResponse {
  success: boolean;
  generateUuid: string;
  originalImageUrl: string;
  extractedPatterns: ExtractedPattern[];
  processingTime: number;
  pointsCost: number;
  error?: string;
}
```

#### 3. 状态查询响应
```typescript
interface PatternExtractionStatusResponse {
  generateStatus: number; // 1-6 状态码
  percentCompleted: number; // 0-1 进度
  images: Array<{
    imageUrl: string;
    auditStatus: number;
    nodeId: string;
    outputName: string;
  }>;
  generateMsg?: string;
  pointsCost: number;
}
```

#### 3. 历史记录接口
```typescript
interface PatternExtractionRecord {
  id: string;
  userId: string;
  originalImageUrl: string;
  extractedPatterns: ExtractedPattern[];
  extractionParameters: PatternExtractionRequest;
  createdAt: Date;
  updatedAt: Date;
}
```

## Data Models

### Database Schema

#### 1. Pattern Extraction History Table
```sql
CREATE TABLE pattern_extraction_history (
  id SERIAL PRIMARY KEY,
  uuid VARCHAR(255) NOT NULL UNIQUE,
  user_uuid VARCHAR(255) NOT NULL,
  original_image_url VARCHAR(500) NOT NULL,
  original_image_name VARCHAR(255),
  file_size INTEGER,
  extraction_subject VARCHAR(255) NOT NULL, -- 提取主体描述
  generate_uuid VARCHAR(255), -- LiblibAI生成任务UUID
  extracted_patterns TEXT, -- JSON array of extracted patterns
  processing_time INTEGER, -- in milliseconds
  points_cost INTEGER DEFAULT 0, -- API调用消耗的积分
  status VARCHAR(50) NOT NULL DEFAULT 'pending', -- pending, processing, completed, failed
  generate_status INTEGER DEFAULT 1, -- LiblibAI状态码 1-6
  percent_completed DECIMAL(3,2) DEFAULT 0, -- 进度 0-1
  error_message TEXT, -- 错误信息
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_pattern_extraction_user_uuid ON pattern_extraction_history(user_uuid);
CREATE INDEX idx_pattern_extraction_created_at ON pattern_extraction_history(created_at);
CREATE INDEX idx_pattern_extraction_generate_uuid ON pattern_extraction_history(generate_uuid);
```

#### 2. Drizzle Schema Definition
```typescript
export const patternExtractionHistory = pgTable("pattern_extraction_history", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  uuid: varchar({ length: 255 }).notNull().unique(),
  user_uuid: varchar({ length: 255 }).notNull(),
  original_image_url: varchar({ length: 500 }).notNull(),
  original_image_name: varchar({ length: 255 }),
  file_size: integer(),
  extraction_type: varchar({ length: 50 }).notNull().default('all'),
  sensitivity: integer().notNull().default(5),
  min_pattern_size: integer().notNull().default(50),
  max_patterns: integer().notNull().default(10),
  extracted_patterns: text(), // JSON string
  processing_time: integer(),
  status: varchar({ length: 50 }).notNull().default('pending'),
  created_at: timestamp({ withTimezone: true }).defaultNow(),
  updated_at: timestamp({ withTimezone: true }).defaultNow(),
});
```

### TypeScript Types

#### 1. 提取的图案类型
```typescript
interface ExtractedPattern {
  id: string;
  type: 'pattern' | 'texture' | 'element';
  imageUrl: string;
  thumbnailUrl: string;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  confidence: number; // 0-1
  characteristics: {
    dominantColors: string[];
    complexity: number; // 1-10
    symmetry: number; // 0-1
    repetition: boolean;
  };
}
```

#### 2. 提取参数类型
```typescript
interface ExtractionParameters {
  extractionType: 'pattern' | 'texture' | 'element' | 'all';
  sensitivity: number; // 1-10, higher = more sensitive
  minPatternSize: number; // minimum pixel size
  maxPatterns: number; // maximum patterns to extract
}
```

## Error Handling

### Error Types
```typescript
enum PatternExtractionErrorType {
  INVALID_FILE_FORMAT = 'invalid_file_format',
  FILE_TOO_LARGE = 'file_too_large',
  PROCESSING_FAILED = 'processing_failed',
  INSUFFICIENT_CREDITS = 'insufficient_credits',
  NETWORK_ERROR = 'network_error',
  STORAGE_ERROR = 'storage_error',
  DATABASE_ERROR = 'database_error'
}
```

### Error Handling Strategy

1. **Client-side Validation**
   - 文件格式验证 (JPG, PNG, WebP, GIF)
   - 文件大小限制 (10MB)
   - 参数范围验证

2. **Server-side Error Handling**
   - 统一错误响应格式
   - 错误日志记录
   - 用户友好的错误消息

3. **UI Error Display**
   - Toast通知显示错误
   - 表单字段错误提示
   - 重试机制

## Testing Strategy

### Unit Testing
- 组件单元测试 (React Testing Library)
- 工具函数测试 (Jest)
- API路由测试
- 数据库操作测试

### Integration Testing
- 端到端用户流程测试
- API集成测试
- 文件上传和处理流程测试

### Performance Testing
- 大文件上传性能测试
- 并发处理能力测试
- 数据库查询性能测试

### Test Coverage Requirements
- 组件测试覆盖率 > 80%
- 业务逻辑测试覆盖率 > 90%
- API测试覆盖率 > 85%

## Security Considerations

### File Upload Security
- 文件类型白名单验证
- 文件内容检查 (magic number)
- 文件大小限制
- 恶意文件扫描

### Data Protection
- 用户数据隔离
- 敏感信息加密存储
- 访问权限控制
- 数据保留策略

### API Security
- 用户身份验证
- 请求频率限制
- 输入参数验证
- CSRF保护

## Performance Optimization

### Frontend Optimization
- 图片懒加载
- 虚拟滚动 (历史记录)
- 组件代码分割
- 缓存策略

### Backend Optimization
- 图片处理异步化
- 数据库查询优化
- 文件存储CDN
- 响应缓存

### Scalability Considerations
- 水平扩展支持
- 负载均衡
- 数据库分片准备
- 微服务架构准备

## Integration Points

### Existing System Integration
- 用户认证系统 (NextAuth.js)
- 积分系统集成
- 通知系统集成
- 文件上传系统复用

### Third-party Services
- LiblibAI ComfyUI API (图案提取服务)
- 云存储服务 (可选)
- CDN服务 (可选)
- 监控和分析服务

### LiblibAI API Integration

#### API Configuration
```typescript
const LIBLIBAI_CONFIG = {
  baseUrl: 'https://openapi.liblibai.cloud/api',
  templateUuid: '4df2efa0f18d46dc9758803e478eb51c',
  workflowUuid: '309906cb93af4c66960625b232cae4a5',
  endpoints: {
    generate: '/generate/comfyui/app',
    status: '/generate/comfy/status'
  }
};
```

#### Pattern Extraction Request Format
```typescript
interface LiblibAIRequest {
  templateUuid: string;
  generateParams: {
    "543": {
      class_type: "LoadImage";
      inputs: {
        image: string; // 图片URL
      };
    };
    "580": {
      class_type: "Primitive string multiline [Crystools]";
      inputs: {
        string: string; // 需要提取的主体描述
      };
    };
    workflowUuid: string;
  };
}
```

#### Pattern Extraction Response Format
```typescript
interface LiblibAIResponse {
  code: number;
  data: {
    accountBalance: number;
    generateStatus: number; // 1-6 状态码
    generateUuid: string;
    images: Array<{
      auditStatus: number;
      imageUrl: string;
      nodeId: string;
      outputName: string;
    }>;
    percentCompleted: number; // 0-1 进度
    pointsCost: number;
    videos: any[];
  };
  msg: string;
}
```

## Deployment Strategy

### Environment Configuration
- 开发环境配置
- 测试环境配置
- 生产环境配置
- 环境变量管理

### Database Migration
- 新表创建脚本
- 数据迁移策略
- 回滚计划
- 性能影响评估

### Feature Rollout
- 功能开关控制
- 灰度发布策略
- 监控和告警
- 回滚准备
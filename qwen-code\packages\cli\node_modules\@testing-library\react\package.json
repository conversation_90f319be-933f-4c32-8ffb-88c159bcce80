{"name": "@testing-library/react", "version": "16.3.0", "description": "Simple and complete React DOM testing utilities that encourage good testing practices.", "main": "dist/index.js", "types": "types/index.d.ts", "module": "dist/@testing-library/react.esm.js", "engines": {"node": ">=18"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "npm-run-all --parallel build:main build:bundle:main build:bundle:pure", "build:bundle:main": "dotenv -e .bundle.main.env kcd-scripts build -- --bundle --no-clean", "build:bundle:pure": "dotenv -e .bundle.main.env -e .bundle.pure.env kcd-scripts build -- --bundle --no-clean", "build:main": "kcd-scripts build --no-clean", "format": "kcd-scripts format", "install:csb": "npm install", "lint": "kcd-scripts lint", "setup": "npm install && npm run validate -s", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot --coverage", "typecheck": "kcd-scripts typecheck --build types", "validate": "kcd-scripts validate"}, "files": ["dist", "dont-cleanup-after-each.js", "pure.js", "pure.d.ts", "types/*.d.ts"], "keywords": ["testing", "react", "ui", "dom", "jsdom", "unit", "integration", "functional", "end-to-end", "e2e"], "author": "<PERSON> <PERSON> <<EMAIL>> (https://kentcdodds.com)", "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.5"}, "devDependencies": {"@testing-library/dom": "^10.0.0", "@testing-library/jest-dom": "^5.11.6", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "chalk": "^4.1.2", "dotenv-cli": "^4.0.0", "jest-diff": "^29.7.0", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "react": "^19.0.0", "react-dom": "^19.0.0", "rimraf": "^3.0.2", "typescript": "^4.1.2"}, "peerDependencies": {"@testing-library/dom": "^10.0.0", "@types/react": "^18.0.0 || ^19.0.0", "@types/react-dom": "^18.0.0 || ^19.0.0", "react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js", "parserOptions": {"ecmaVersion": 2022}, "globals": {"globalThis": "readonly"}, "rules": {"react/prop-types": "off", "react/no-adjacent-inline-elements": "off", "import/no-unassigned-import": "off", "import/named": "off", "testing-library/no-container": "off", "testing-library/no-debugging-utils": "off", "testing-library/no-dom-import": "off", "testing-library/no-unnecessary-act": "off", "testing-library/prefer-explicit-assert": "off", "testing-library/prefer-find-by": "off", "testing-library/prefer-user-event": "off"}}, "eslintIgnore": ["node_modules", "coverage", "dist", "*.d.ts"], "repository": {"type": "git", "url": "https://github.com/testing-library/react-testing-library"}, "bugs": {"url": "https://github.com/testing-library/react-testing-library/issues"}, "homepage": "https://github.com/testing-library/react-testing-library#readme"}
5.1 F.1 Kontext - 文生图
5.1.1 接口定义
- 请求地址：
POST  /api/generate/kontext/text2img
- headers：
header
value
备注
Content-Type
application/json

- 请求body：
参数
类型
是否必需
说明
备注
templateUuid
string
是
fe9928fde1b4491c9b360dd24aa2b115

generateParams
object
是
生图参数，json结构
参数中的图片字段需提供可访问的完整图片地址


5.1.2 参数说明
变量名
格式
备注
数值范围
必填
示例
model
enums
模型
- pro
- max：默认
否

prompt

string
正向提示词，文本
- 不超过2000字符
是

{
    "templateUuid":"fe9928fde1b4491c9b360dd24aa2b115",
    "generateParams":{
        "model":"pro",
        "prompt":"画一个LibLib公司的品牌海报",
        "aspectRatio":"3:4",
        "guidance_scale":3.5,
        "imgCount":1      
    }
}

aspectRatio

enums
图片宽高比
- 1:1 - 默认
- 2:3
- 3:2
- 3:4
- 4:3
- 9:16
- 16:9
- 9:21
- 21:9

否


imgCount
int
单次生图张数
1. 默认值：1
2. 阈值范围：1 ~ 4
否

guidance_scale
double
提示词引导系数
1. 默认值：3.5
2. 阈值范围：1.0 ~ 20.0
否

5.1.3 返回值
参数
类型
备注
generateUuid
string
生图任务uuid，使用该uuid查询生图进度

5.2 F.1 Kontext - 图生图（指令编辑&多图参考）
5.2.1 接口定义
- 请求地址：
POST  /api/generate/kontext/img2img
- headers：
header
value
Content-Type
application/json
- 请求body：
参数
类型
是否必需
说明
备注
templateUuid
string
是
- 1c0a9712b3d84e1b8a9f49514a46d88c

generateParams
object
是
生图参数，json结构
参数中的图片字段需提供可访问的完整图片地址
5.2.2 参数说明
变量名
格式
备注
数值范围
必填
示例
model
enums
模型
- pro：暂不支持多图参考
- max：默认
否

prompt

string
正向提示词，文本
- 不超过2000字符
是

{
    "templateUuid":"1c0a9712b3d84e1b8a9f49514a46d88c",
    "generateParams":{
        "prompt":"Turn this image into a Ghibli-style, a traditional Japanese anime aesthetics.",
        "aspectRatio":"2:3",
        "guidance_scale":3.5,
        "imgCount":1,
        "image_list":[
            "https://liblibai-online.liblib.cloud/img/081e9f07d9bd4c2ba090efde163518f9/3c65a38d7df2589c4bf834740385192128cf035c7c779ae2bbbc354bf0efcfcb.png"]      
    }
}
aspectRatio

enums
图片宽高比
- 1:1 - 默认
- 2:3
- 3:2
- 3:4
- 4:3
- 9:16
- 16:9
- 9:21
- 21:9

否


imgCount
int
单次生图张数
1. 默认值：1
2. 阈值范围：1 ~ 4
否

guidance_scale
double
提示词引导系数
1. 默认值：3.5
2. 阈值范围：1.0 ~ 20.0
否

image_list

Array
参考图
- 图片数量：1~4，可公网访问的图片地址
- 图片格式：PNG, JPG, JPEG, WEBP
- 图片大小：每张图都不超过10MB
是


5.2.3 返回值
参数
类型
备注
generateUuid
string
生图任务uuid，使用该uuid查询生图进度
5.3 查询任务结果
5.3.1 接口定义

说明
接口定义
- 接口：POST  /api/generate/status
- 请求body：
参数
类型
是否必需
备注
generateUuid
string
是
生图任务uuid，发起生图任务时返回该字段
5.3.2 返回值
参数
类型
备注
generateUuid
string
生图任务uuid，使用该uuid查询生图进度
generateStatus
int
生图状态见下方3.3.1节
percentCompleted
float
生图进度（智能算法IMG1不支持）
generateMsg
string
生图信息，提供附加信息，如生图失败信息
pointsCost
int
本次生图任务消耗积分数
accountBalance
int
账户剩余积分数
images
[]object
图片列表，只提供审核通过的图片
images.0.imageUrl
string
图片地址，可直接访问，地址有时效性：7天
images.0.seed
int
随机种子值（智能算法IMG1不支持）
images.0.auditStatus
int
审核状态说明

import hmac
import time
import requests
from datetime import datetime
import hashlib
import uuid
import base64


class SmartImg1:
    def __init__(self, ak='', sk='', interval=5):
        """
        :param ak
        :param sk
        :param interval 轮询间隔
        """
        self.ak = ak
        self.sk = sk
        self.time_stamp = int(datetime.now().timestamp() * 1000)  # 毫秒级时间戳
        self.signature_nonce = uuid.uuid1()  # 随机字符串
        self.signature_kontext_text2img = self._hash_kontext_text2img_sk(self.sk, self.time_stamp, self.signature_nonce)
        self.signature_kontext_img2img = self._hash_kontext_img2img_sk(self.sk, self.time_stamp, self.signature_nonce)
        self.signature_task_status = self._hash_task_status_sk(self.sk, self.time_stamp, self.signature_nonce)
        self.interval = interval
        self.headers = {'Content-Type': 'application/json'}
        self.kontext_text2img = self.kontext_text2img(self.ak, self.signature_kontext_text2img, self.time_stamp,
                                                  self.signature_nonce)
        self.kontext_img2img = self.kontext_img2img(self.ak, self.signature_kontext_img2img, self.time_stamp,
                                                  self.signature_nonce)
        self.get_task_status = self.get_task_status_url(self.ak, self.signature_task_status, self.time_stamp,
                                                               self.signature_nonce)
    def hmac_sha1(self, key, code):
        hmac_code = hmac.new(key.encode(), code.encode(), hashlib.sha1)
        return hmac_code.digest()

    def _hash_kontext_text2img_sk(self, key, s_time, ro):
        """加密sk"""
        data = "/api/generate/kontext/text2img" + "&" + str(s_time) + "&" + str(ro)
        s = base64.urlsafe_b64encode(self.hmac_sha1(key, data)).rstrip(b'=').decode()
        return s
    
    def _hash_kontext_img2img_sk(self, key, s_time, ro):
        """加密sk"""
        data = "/api/generate/kontext/img2img" + "&" + str(s_time) + "&" + str(ro)
        s = base64.urlsafe_b64encode(self.hmac_sha1(key, data)).rstrip(b'=').decode()
        return s

    def _hash_task_status_sk(self, key, s_time, ro):
        """加密sk"""
        data = "/api/generate/status" + "&" + str(s_time) + "&" + str(ro)
        s = base64.urlsafe_b64encode(self.hmac_sha1(key, data)).rstrip(b'=').decode()
        return s

    def kontext_text2img(self, ak, signature, time_stamp, signature_nonce):
        url = f"https://openapi.liblibai.cloud/api/generate/kontext/text2img?AccessKey={ak}&Signature={signature}&Timestamp={time_stamp}&SignatureNonce={signature_nonce}"
        return url
    
    def kontext_img2img(self, ak, signature, time_stamp, signature_nonce):
        url = f"https://openapi.liblibai.cloud/api/generate/kontext/img2img?AccessKey={ak}&Signature={signature}&Timestamp={time_stamp}&SignatureNonce={signature_nonce}"
        return url    

    def get_task_status_url(self, ak, signature, time_stamp, signature_nonce):

        url = f"https://openapi.liblibai.cloud/api/generate/status?AccessKey={ak}&Signature={signature}&Timestamp={time_stamp}&SignatureNonce={signature_nonce}"
        return url


    def kontext_text2img_generate(self):        
        base_json = {
            "templateUuid":"fe9928fde1b4491c9b360dd24aa2b115",
            "generateParams":{
                "model":"pro",
                "prompt":"为现实主义电影摄影作品创作一张封面海报，具有艺术感染力，标题为“Liblib”。文字应用白笔书写。整体形象应具有电影海报的风格。不要使用黑白图像。",
                "aspectRatio":"3:4",
                "guidance_scale":3.5,
                "imgCount":1      
            }
        }
        self.run(base_json, self.kontext_text2img)

    def kontext_img2img_edit(self):        
        base_json = {
            "templateUuid":"1c0a9712b3d84e1b8a9f49514a46d88c",
            "generateParams":{
                "prompt":"Turn this image into a Ghibli-style, a traditional Japanese anime aesthetics.",
                "aspectRatio":"2:3",
                "guidance_scale":3.5,
                "imgCount":1,
                "image_list":[
                    "https://liblibai-online.liblib.cloud/img/081e9f07d9bd4c2ba090efde163518f9/10d686ff178fb603bec49e84eed8a5d95c20d969cf3ea4abb83d11caff80fd34.jpg"]      
            }
        }
        self.run(base_json, self.kontext_img2img)

    def kontext_img2img_multiImage(self):        
        base_json = {
            "templateUuid":"1c0a9712b3d84e1b8a9f49514a46d88c",
            "generateParams":{
                "prompt":"让女孩坐在第二张图的椅子上看书",
                "aspectRatio":"3:4",
                "guidance_scale":3.5,
                "imgCount":1,
                "image_list":[
                    "https://liblibai-online.liblib.cloud/img/081e9f07d9bd4c2ba090efde163518f9/10d686ff178fb603bec49e84eed8a5d95c20d969cf3ea4abb83d11caff80fd34.jpg",
                    "https://liblibai-online.liblib.cloud/img/081e9f07d9bd4c2ba090efde163518f9/92cc6b39931ed0932dfe49a7b354ce1a8f6ede819ccbf8a9f3a2fc315b0be42a.png"
                    ]
                }        
            }
        self.run(base_json, self.kontext_img2img)

    def run(self, data, url, timeout=300):
        """
        发送任务到生图接口，直到返回image为止，失败抛出异常信息
        """
        start_time = time.time()  # 记录开始时间
        # 这里提交任务，校验是否提交成功，并且获取任务ID
        print(url)
        response = requests.post(url=url, headers=self.headers, json=data)
        response.raise_for_status()
        progress = response.json()
        print(progress)
        if progress['code'] == 0:
            # 如果获取到任务ID，执行等待生图
            while True:
                current_time = time.time()
                if (current_time - start_time) > timeout:
                    print(f"{timeout}s任务超时，已退出轮询。")
                    return None

                generate_uuid = progress["data"]['generateUuid']
                data = {"generateUuid": generate_uuid}
                response = requests.post(url=self.get_task_status, headers=self.headers, json=data)
                response.raise_for_status()
                progress = response.json()
                print(progress)

                if progress['data'].get('images') and any(
                        image for image in progress['data']['images'] if image is not None):
                    print("任务完成，获取到图像数据。")
                    return progress

                print(f"任务尚未完成，等待 {self.interval} 秒...")
                time.sleep(self.interval)
        else:
            return f'任务失败,原因：code {progress["msg"]}'


def main():
    test = SmartImg1()
    start_time = time.time()
    # test.kontext_text2img_generate()
    test.kontext_img2img_edit()
    # test.kontext_img2img_multiImage()
    end_time = time.time()
    time_diff = end_time - start_time
    print('任务耗时：', time_diff)


if __name__ == '__main__':
    main()

    2.1 访问地址
Liblib开放平台域名：https://openapi.liblibai.cloud（无法直接打开，需配合密钥访问）
2.2 计费规则
非固定消耗，每次生图任务消耗的积分与以下参数有关：
- 选用模型
- 采样步数（steps）
- 采样方法（sampler，SDE系列会产生额外消耗）
- 生成图片宽度
- 生成图片高度
- 生成图片张数
- 重绘幅度（denoisingStrength）
- 高分辨率修复的重绘步数和重绘幅度
- Controlnet数量
2.3 并发数和QPS
- 生图任务并发数，默认5（因生图需要时间，指同时可进行的生图任务数）
- 发起生图任务接口，QPS默认1秒1次，（可用每天预计生图张数/24h/60m/60s来估算平均值）
- 查询生图结果接口，QPS无限制
2.4 生成API密钥
在登录Liblib领取API试用积分或购买API积分后，Liblib会生成开放平台访问密钥，用于后续API接口访问，密钥包括：
- AccessKey，API访问凭证，唯一识别访问用户，长度通常在20-30位左右，如：KIQMFXjHaobx7wqo9XvYKA
- SecretKey，API访问密钥，用于加密请求参数，避免请求参数被篡改，长度通常在30位以上，如：KppKsn7ezZxhi6lIDjbo7YyVYzanSu2d
2.4.1 使用密钥
申请API密钥之后，需要在每次请求API接口的查询字符串中固定传递以下参数：
参数
类型
是否必需
说明
AccessKey
String
是
开通开放平台授权的访问AccessKey
Signature
String
是
加密请求参数生成的签名，签名公式见下节“生成签名”
Timestamp
String
是
生成签名时的毫秒时间戳，整数字符串，有效期5分钟
SignatureNonce
String
是
生成签名时的随机字符串
如请求地址：https://test.xxx.com/api/genImg?AccessKey=KIQMFXjHaobx7wqo9XvYKA&Signature=test1232132&Timestamp=1725458584000&SignatureNonce=random1232
2.4.2 生成签名
签名生成公式如下：
# 1. 用"&"拼接参数
# URL地址：以上方请求地址为例，为“/api/genImg”
# 毫秒时间戳：即上节“使用密钥”中要传递的“Timestamp”
# 随机字符串：即上节“使用密钥”中要传递的“SignatureNonce”
原文 = URL地址 + "&" + 毫秒时间戳 + "&" + 随机字符串
# 2. 用SecretKey加密原文，使用hmacsha1算法
密文 = hmacSha1(原文, SecretKey)
# 3. 生成url安全的base64签名
# 注：base64编码时不要补全位数
签名 = encodeBase64URLSafeString(密文)